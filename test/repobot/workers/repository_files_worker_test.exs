defmodule Repobot.Workers.RepositoryFilesWorkerTest do
  use Repobot.DataCase, async: true
  use Oban.Testing, repo: Repobot.Repo

  import Repobot.Test.Fixtures
  import Mox

  alias Repobot.Workers.RepositoryFilesWorker
  alias Repobot.Test.GitHubMock

  setup :verify_on_exit!

  describe "perform/1 - tree loading" do
    test "successfully loads repository trees" do
      user = create_user()

      # Create repositories without files (need tree loading)
      repo1 =
        create_repository(%{
          name: "repo1",
          owner: user.login,
          full_name: "#{user.login}/repo1",
          organization_id: user.default_organization_id
        })

      repo2 =
        create_repository(%{
          name: "repo2",
          owner: user.login,
          full_name: "#{user.login}/repo2",
          organization_id: user.default_organization_id
        })

      # Mock GitHub API for tree loading
      GitHubMock
      |> stub(:client, fn _user -> :test_client end)
      |> expect(:get_tree, 2, fn :test_client, _owner, repo_name ->
        case repo_name do
          "repo1" ->
            {:ok,
             [
               %{"path" => "README.md", "type" => "file", "sha" => "readme123", "size" => 100},
               %{"path" => "LICENSE", "type" => "file", "sha" => "license123", "size" => 200}
             ]}

          "repo2" ->
            {:ok,
             [
               %{"path" => "README.md", "type" => "file", "sha" => "readme456", "size" => 150},
               %{"path" => "config.json", "type" => "file", "sha" => "config123", "size" => 50}
             ]}
        end
      end)

      # Subscribe to notifications
      topic = "repository_files:#{user.id}"
      topic_atom = String.to_atom(topic)
      Oban.Notifier.listen(Oban, topic_atom)

      job_args = %{
        "user_id" => user.id,
        "repository_ids" => [repo1.id, repo2.id],
        "operation" => "load_trees",
        "topic" => topic
      }

      assert :ok = perform_job(RepositoryFilesWorker, job_args)

      # Should receive completion notification
      assert_receive {:notification, :repository_files,
                      %{
                        "status" => "ok",
                        "operation" => "load_trees",
                        "repository_ids" => repository_ids
                      }}

      assert Enum.sort(repository_ids) == Enum.sort([repo1.id, repo2.id])

      # Verify files were created
      repo1_reloaded =
        Repobot.Repo.preload(Repobot.Repositories.get_repository!(repo1.id), :files)

      repo2_reloaded =
        Repobot.Repo.preload(Repobot.Repositories.get_repository!(repo2.id), :files)

      assert length(repo1_reloaded.files) == 2
      assert length(repo2_reloaded.files) == 2

      # Check specific files
      readme1 = Enum.find(repo1_reloaded.files, &(&1.path == "README.md"))
      license1 = Enum.find(repo1_reloaded.files, &(&1.path == "LICENSE"))
      readme2 = Enum.find(repo2_reloaded.files, &(&1.path == "README.md"))
      config2 = Enum.find(repo2_reloaded.files, &(&1.path == "config.json"))

      assert readme1.sha == "readme123"
      assert license1.sha == "license123"
      assert readme2.sha == "readme456"
      assert config2.sha == "config123"
    end

    test "handles repositories that already have files" do
      user = create_user()

      # Create repository with existing files
      repo =
        create_repository(%{
          name: "existing-repo",
          owner: user.login,
          full_name: "#{user.login}/existing-repo",
          organization_id: user.default_organization_id
        })

      # Add existing file
      create_repository_file(%{
        repository_id: repo.id,
        path: "existing.txt",
        content: "existing content",
        size: 100,
        sha: "existing123"
      })

      # Subscribe to notifications
      topic = "repository_files:#{user.id}"
      topic_atom = String.to_atom(topic)
      Oban.Notifier.listen(Oban, topic_atom)

      job_args = %{
        "user_id" => user.id,
        "repository_ids" => [repo.id],
        "operation" => "load_trees",
        "topic" => topic
      }

      # Should not call GitHub API since repo already has files
      GitHubMock
      |> stub(:client, fn _user -> :test_client end)

      assert :ok = perform_job(RepositoryFilesWorker, job_args)

      # Should receive completion notification immediately
      repo_id = repo.id

      assert_receive {:notification, :repository_files,
                      %{
                        "status" => "ok",
                        "operation" => "load_trees",
                        "repository_ids" => [^repo_id]
                      }}
    end
  end

  describe "perform/1 - content refresh" do
    test "successfully refreshes file content" do
      user = create_user()

      # Create repositories with files but no content
      repo1 =
        create_repository(%{
          name: "repo1",
          owner: user.login,
          full_name: "#{user.login}/repo1",
          organization_id: user.default_organization_id
        })

      create_repository_file(%{
        repository_id: repo1.id,
        path: "test.txt",
        content: nil,
        size: 100,
        sha: "test123"
      })

      # Mock GitHub API for content refresh
      GitHubMock
      |> stub(:client, fn _user -> :test_client end)
      |> expect(:get_file_content, 1, fn :test_client, _owner, "repo1", "test.txt" ->
        {:ok, "refreshed content", %{}}
      end)

      # Subscribe to notifications
      topic = "repository_files:#{user.id}"
      topic_atom = String.to_atom(topic)
      Oban.Notifier.listen(Oban, topic_atom)

      job_args = %{
        "user_id" => user.id,
        "repository_ids" => [repo1.id],
        "operation" => "refresh_content",
        "topic" => topic
      }

      assert :ok = perform_job(RepositoryFilesWorker, job_args)

      # Should receive completion notification
      assert_receive {:notification, :repository_files,
                      %{
                        "status" => "ok",
                        "operation" => "refresh_content",
                        "refreshed_repos" => refreshed_repos
                      }}

      assert length(refreshed_repos) == 1
      refreshed_repo = hd(refreshed_repos)
      assert refreshed_repo["id"] == repo1.id

      # Verify content was updated
      file = Repobot.RepositoryFiles.get_repository_file_by_path!(repo1.id, "test.txt")
      assert file.content == "refreshed content"
      assert file.content_updated_at != nil
    end
  end
end
